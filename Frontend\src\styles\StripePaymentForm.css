/* Scoped Stripe Payment Form Styles - Prevents conflicts with other app styles */
.stripe-payment-container .stripe-payment-form {
  max-width: 100%;
  margin: 0;
  padding: var(--heading5);
  background: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
}

.stripe-payment-container .payment-header {
  text-align: left;
  margin-bottom: var(--heading5);
  padding-bottom: 0;
  border-bottom: none;
}

.stripe-payment-container .payment-header h3 {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--smallfont);
}

.stripe-payment-container .payment-header p {
  color: var(--secondary-color);
  font-size: var(--basefont);
  margin: 0 0 var(--heading5) 0;
  font-weight: 400;
}

.stripe-payment-container .payment-form {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.stripe-payment-container .billing-details {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.stripe-payment-container .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.stripe-payment-container .form-group label {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  margin-bottom: 6px;
}

.stripe-payment-container .form-input {
  padding: var(--basefont) var(--heading6);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: all 0.3s ease;
  font-family: inherit;
  min-height: 50px;
  position: relative;
}

.stripe-payment-container .form-input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
  transform: translateY(-1px);
}

.stripe-payment-container .form-input:hover {
  border-color: var(--btn-color);
  box-shadow: 0 2px 8px rgba(238, 52, 37, 0.1);
}

.stripe-payment-container .card-element-container {
  padding: var(--basefont) var(--heading6);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  background: var(--white);
  transition: all 0.3s ease;
  min-height: 50px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.stripe-payment-container .card-element-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: transparent;
  transition: background 0.3s ease;
}

.stripe-payment-container .card-element-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
  transform: translateY(-1px);
}

.stripe-payment-container .card-element-container:focus-within::before {
  background: var(--btn-color);
}

.stripe-payment-container .card-element-container:hover {
  border-color: var(--btn-color);
  box-shadow: 0 2px 8px rgba(238, 52, 37, 0.1);
}

.stripe-payment-container .card-element-loading {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--smallfont);
  padding: var(--smallfont) 0;
}

.stripe-payment-container .card-element-loading .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--light-gray);
  border-top: 2px solid var(--btn-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.stripe-payment-container .card-element-placeholder {
  display: flex;
  align-items: center;
  color: var(--dark-gray);
  font-size: var(--smallfont);
  padding: var(--smallfont) 0;
  opacity: 0.7;
}

.stripe-payment-container .stripe-card-element {
  width: 100%;
  min-height: 20px;
}

.stripe-payment-container .stripe-card-element-wrapper {
  width: 100%;
  min-height: 40px;
  display: flex;
  align-items: center;
}

.stripe-payment-container .card-error {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 6px;
  padding: var(--smallfont);
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: 8px;
}

.stripe-payment-container .card-error::before {
  content: "⚠️";
  font-size: var(--basefont);
}

.stripe-payment-container .payment-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: var(--error-color);
  padding: var(--basefont);
  border-radius: var(--border-radius-medium);
  text-align: center;
}

.stripe-payment-container .order-summary-payment {
  background: var(--bg-gray);
  padding: var(--heading6);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
  margin-top: var(--basefont);
}

.stripe-payment-container .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) 0;
  font-size: var(--smallfont);
  color: var(--text-color);
}

.stripe-payment-container .summary-row.total {
  border-top: 1px solid var(--light-gray);
  margin-top: var(--smallfont);
  padding-top: var(--basefont);
  font-weight: 600;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.stripe-payment-container .payment-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
  margin-top: var(--heading6);
  padding-top: var(--heading6);
  border-top: 1px solid var(--light-gray);
}

.stripe-payment-container .cancel-btn {
  flex: 1;
  padding: var(--smallfont) var(--heading5);
  border: 1px solid var(--light-gray);
  background: var(--white);
  color: var(--dark-gray);
  border-radius: var(--border-radius-medium);
  font-weight: 500;
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.stripe-payment-container .cancel-btn:hover:not(:disabled) {
  background: var(--bg-gray);
  border-color: var(--dark-gray);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-light);
}

.stripe-payment-container .pay-btn {
  flex: 2;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
}

.stripe-payment-container .pay-btn:hover:not(:disabled) {
 transform: scale(1.02);
}

.stripe-payment-container .pay-btn:disabled {
  background: var(--dark-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.stripe-payment-container .pay-btn.processing {
  position: relative;
  color: transparent;
}

.stripe-payment-container .pay-btn.processing::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stripe-payment-container .payment-method-selection,
.stripe-payment-container .billing-details,
.stripe-payment-container .form-group {
  animation: fadeInUp 0.6s ease-out;
}

.stripe-payment-container .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.stripe-payment-container .security-notice {
  text-align: center;
  margin-top: var(--heading5);
  padding: var(--basefont);
  background: linear-gradient(135deg, var(--bg-blue) 0%, var(--primary-light-color) 100%);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
}

.stripe-payment-container .security-notice p {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
}

/* Responsive Design */
@media (max-width: 768px) {
  .stripe-payment-container .payment-header h3 {
    font-size: var(--heading6);
  }

  .stripe-payment-container .payment-actions {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .stripe-payment-container .cancel-btn,
  .stripe-payment-container .pay-btn {
    flex: none;
    padding: var(--basefont) var(--heading5);
  }

  .stripe-payment-container .form-input {
    font-size: var(--basefont);
  }

  .stripe-payment-container .payment-method-selection {
    padding: var(--basefont);
  }

  .stripe-payment-container .card-info {
    gap: var(--smallfont);
  }

  .stripe-payment-container .card-icon {
    min-width: 36px;
    height: 36px;
    font-size: var(--basefont);
  }
}

/* Loading state overlay */
.stripe-payment-container .payment-form.processing {
  position: relative;
  pointer-events: none;
}

.stripe-payment-container .payment-form.processing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  z-index: 10;
}

/* Payment Method Selection Styles */
.stripe-payment-container .payment-method-selection {
  margin-bottom: var(--heading5);
  padding: 0;
  border: none;
  border-radius: 0;
  box-shadow: none;
  position: relative;
  overflow: visible;
}

.stripe-payment-container .payment-method-selection h4 {
  display: none;
}

.stripe-payment-container .payment-method-subtitle {
  color: var(--secondary-color);
  font-size: var(--basefont);
  margin: 0 0 var(--heading5) 0;
  font-weight: 400;
}

.stripe-payment-container .saved-cards-header {
  color: var(--text-color);
  font-size: var(--basefont);
  font-weight: 600;
  margin: 0 0 var(--basefont) 0;
}

.stripe-payment-container .payment-option {
  margin-bottom: 0;
  padding: 0;
  border-radius: 0;
  transition: none;
  border: none;
  background: transparent;
  display: none;
  gap: 0;
}

.stripe-payment-container .payment-option:last-child {
  margin-bottom: 0;
}

.stripe-payment-container .payment-option:hover {
  background: transparent;
  border-color: transparent;
  transform: none;
  box-shadow: none;
}

.stripe-payment-container .payment-option input[type="radio"] {
  display: none;
}

.stripe-payment-container .payment-option label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--secondary-color);
  font-size: var(--basefont);
  transition: all 0.3s ease;
  width: 100%;
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  margin-bottom: var(--basefont);
  background: var(--white);
}

.stripe-payment-container .payment-option input[type="radio"]:checked + label {
  color: var(--btn-color);
  font-weight: 600;
  border-color: var(--btn-color);
  background: rgba(236, 29, 59, 0.02);
}

.stripe-payment-container .payment-option:has(input[type="radio"]:checked) {
  background: transparent;
}

.stripe-payment-container .payment-option:last-child {
  display: flex !important;
}

.stripe-payment-container .payment-option:last-child label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-color);
  font-size: var(--basefont);
  transition: all 0.3s ease;
  width: 100%;
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  margin-bottom: 0;
  background: var(--white);
  gap: var(--smallfont);
}

.stripe-payment-container .payment-option:last-child label:hover {
  border-color: var(--btn-color);
  color: var(--btn-color);
}

.stripe-payment-container .plus-icon {
  margin-right: 0;
  color: var(--btn-color);
  font-size: var(--basefont);
}

/* Saved Cards List */
.stripe-payment-container .saved-cards-section {
  margin-bottom: var(--basefont);
  margin-top: 0;
}

.stripe-payment-container .saved-cards-section .payment-option {
  display: none;
}

.stripe-payment-container .saved-cards-section::before {
  content: "Saved Cards";
  display: block;
  color: var(--text-color);
  font-size: var(--basefont);
  font-weight: 600;
  margin-bottom: var(--basefont);
}

.stripe-payment-container .saved-cards-list {
  margin-top: 0;
  margin-bottom: var(--basefont);
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
  padding: 0;
  background: transparent;
  border-radius: 0;
  border: none;
}

.stripe-payment-container .saved-card-item {
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  background: var(--white);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  cursor: pointer;
  justify-content: space-between;
}

/* Add delete button as pseudo-element to match Figma design */
.stripe-payment-container .saved-card-item::after {
  content: "🗑";
  position: absolute;
  right: var(--basefont);
  top: 50%;
  transform: translateY(-50%);
  color: var(--error-color);
  font-size: var(--basefont);
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.stripe-payment-container .saved-card-item:hover::after {
  opacity: 1;
}

.stripe-payment-container .saved-card-item:hover {
  border-color: var(--light-gray);
  box-shadow: none;
  transform: none;
}

.stripe-payment-container .saved-card-item:has(input[type="radio"]:checked) {
  border-color: var(--btn-color);
  background: rgba(238, 52, 37, 0.02);
  box-shadow: none;
}

.stripe-payment-container .saved-card-item:has(input[type="radio"]:checked)::before {
  background: var(--btn-color);
}

.stripe-payment-container .saved-card-item input[type="radio"]:checked + .card-label {
  color: var(--text-color);
}

.stripe-payment-container .saved-card-item input[type="radio"] {
  display: none;
}

.stripe-payment-container .card-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: calc(100% - 40px); /* Account for delete button space */
  font-weight: 500;
}

.stripe-payment-container .card-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  flex: 1;
}

.stripe-payment-container .card-icon {
  width: 32px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  flex-shrink: 0;
}

/* Override the FaCreditCard icon with our custom card icons */
.stripe-payment-container .card-icon.mastercard,
.stripe-payment-container .card-icon svg[data-icon="credit-card"] {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAzMiAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iI0ZGNTUwMCIvPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjEwIiByPSI2IiBmaWxsPSIjRkY1NTAwIi8+CjxjaXJjbGUgY3g9IjIwIiBjeT0iMTAiIHI9IjYiIGZpbGw9IiNGRkY1RjAiLz4KPC9zdmc+');
  font-size: 0;
  color: transparent;
}

.stripe-payment-container .card-icon.visa {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAzMiAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzAwNTFBNSIvPgo8dGV4dCB4PSI1IiB5PSIxNCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEwIiBmaWxsPSJ3aGl0ZSIgZm9udC13ZWlnaHQ9ImJvbGQiPlZJU0E8L3RleHQ+Cjwvc3ZnPg==');
}

.stripe-payment-container .card-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  align-items: flex-start;
}

.stripe-payment-container .cards-details-style {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-start;
  width: 100%;
}

.stripe-payment-container .card-number {
  font-weight: 400;
  color: var(--text-color);
  font-size: var(--basefont);
  letter-spacing: 1px;
}

.stripe-payment-container .card-type {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.stripe-payment-container .card-expiry {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.stripe-payment-container .card-type-expiry {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Style the gap-10 flex div from the component */
.stripe-payment-container .gap-10.flex {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.stripe-payment-container .delete-card-btn {
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  padding: 0;
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  font-size: var(--basefont);
}

.stripe-payment-container .delete-card-btn:hover {
  color: var(--error-color);
}

/* Add a trash icon for the delete button */
.stripe-payment-container .delete-card-btn::after {
  content: "×";
  font-size: 20px;
  font-weight: bold;
  color: var(--error-color);
}

.stripe-payment-container .default-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  background: #28a745;
  color: var(--white);
  font-size: var(--extrasmallfont);
  border-radius: var(--border-radius);
  font-weight: 600;
  text-transform: uppercase;
  width: fit-content;
  letter-spacing: 0.5px;
  margin-left: auto;
  position: absolute;
  right: 40px;
}

.stripe-payment-container .default-badge::before {
  content: "✓";
  margin-right: 4px;
  font-size: var(--extrasmallfont);
}

/* Add New Card Button */
.stripe-payment-container .add-new-card-btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  background: var(--white);
  color: var(--text-color);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: flex-start;
  margin-top: var(--smallfont);
}

.stripe-payment-container .add-new-card-btn:hover {
  border-color: var(--btn-color);
  color: var(--btn-color);
}

.stripe-payment-container .add-new-card-btn .plus-icon {
  color: var(--btn-color);
  font-size: var(--basefont);
  margin-right: 0;
}

/* Responsive Design for Payment Method Selection */
@media (max-width: 768px) {
  .stripe-payment-container .payment-method-selection {
    padding: 0;
  }

  .stripe-payment-container .card-info {
    gap: var(--smallfont);
  }

  .stripe-payment-container .card-details {
    gap: 2px;
  }

  .stripe-payment-container .saved-card-item {
    padding: var(--basefont);
  }

  .stripe-payment-container .card-icon {
    width: 28px;
    height: 18px;
  }
}

@media (max-width: 500px) {
  .stripe-payment-container .card-details {
    gap: 2px;
    display: flex;
    flex-direction: column;
  }

  .stripe-payment-container .payment-method-selection {
    padding: 0;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .stripe-payment-container .saved-cards-list {
    padding: 0;
    border: none;
  }

  .stripe-payment-container .card-info {
    gap: var(--smallfont);
  }

  .stripe-payment-container .saved-card-item {
    padding: var(--smallfont);
  }

  .stripe-payment-container .card-icon {
    width: 24px;
    height: 16px;
  }
}